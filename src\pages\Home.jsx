import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import GhostlySmoke from '../components/GhostlySmoke'
import SpiderWeb from '../components/SpiderWeb'
import SpiderAtmosphere from '../components/SpiderAtmosphere'
import HorizonTitleAnimation from '../components/HorizonTitleAnimation'
import HorizonSubtitleAnimation from '../components/HorizonSubtitleAnimation'
import SectionCard from '../components/SectionCard'

const Home = () => {
  const [animationStage, setAnimationStage] = useState('ghostlySettle') // ghostlySettle, titleAppear, subtitleAppear, showSections
  const [showTitle, setShowTitle] = useState(false)
  const [showSubtitle, setShowSubtitle] = useState(false)
  const [showSections, setShowSections] = useState(false)

  const sections = [
    {
      id: 'sketch',
      title: 'Sketch / Dessin',
      description: 'Galerie d\'images cliquables avec histoires sonores',
      path: '/sketch',
      color: '#00fffc',
      icon: '✏️'
    },
    {
      id: '3d-art',
      title: '3D Art',
      description: 'Images et vidéos de modèles 3D avec effets hover',
      path: '/3d-art',
      color: '#ff00f7',
      icon: '🎨'
    },
    {
      id: 'unity',
      title: 'Unity / Game Development',
      description: 'Vidéos gameplay mobile et VR avec interactions',
      path: '/unity',
      color: '#ffdd00',
      icon: '🎮'
    },
    {
      id: 'scenario',
      title: 'Scenario / Storytelling',
      description: 'Textes, images et vidéos de scénarios et concepts',
      path: '/scenario',
      color: '#8a2be2',
      icon: '📖'
    }
  ]

  useEffect(() => {
    const sequence = [
      { stage: 'titleAppear', delay: 1000 },      // Title Horizon animation starts
      { stage: 'subtitleAppear', delay: 5000 },   // Subtitle Horizon animation starts after title completes
      { stage: 'showSections', delay: 10000 }     // Sections emerge after both animations complete
    ]

    const timers = sequence.map(({ stage, delay }) =>
      setTimeout(() => {
        if (stage === 'titleAppear') {
          setShowTitle(true)
        } else if (stage === 'subtitleAppear') {
          setShowSubtitle(true)
        } else if (stage === 'showSections') {
          setShowSections(true)
        }
        setAnimationStage(stage)
      }, delay)
    )

    return () => timers.forEach(clearTimeout)
  }, [])

  return (
    <div className="relative min-h-screen overflow-hidden" style={{ background: '#000000' }}>
      <SpiderAtmosphere intensity={1} />
      <SpiderWeb intensity={1.2} />
      <GhostlySmoke intensity={1.5} interactive={showSubtitle} spiderTheme={true} />

      {/* Main Horizon Animation Container */}
      <div className="min-h-screen flex items-center justify-center relative">

        {/* Horizon Title Animation */}
        <AnimatePresence>
          {showTitle && (
            <motion.div className="absolute inset-0">
              <HorizonTitleAnimation
                text="DIANA MRABET"
                isVisible={showTitle}
                onComplete={() => console.log('Title animation complete')}
                fontSize="clamp(3rem, 8vw, 6rem)"
                fontFamily='Tisk, "Black Ops One", "Orbitron", Impact, sans-serif'
                delay={0}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Horizon Subtitle Animation */}
        <AnimatePresence>
          {showSubtitle && (
            <motion.div className="absolute inset-0">
              <HorizonSubtitleAnimation
                text="3D ARTIST AND GAME DEVELOPER"
                isVisible={showSubtitle}
                onComplete={() => console.log('Subtitle animation complete')}
                delay={0}
              />
            </motion.div>
          )}
        </AnimatePresence>



        {/* Portfolio Sections - Emerging from Darkness */}
        <AnimatePresence>
          {showSections && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 3, ease: "easeInOut" }}
              className="absolute inset-0 flex items-center justify-center p-8"
              style={{ perspective: '1000px' }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl w-full">
                {sections.map((section, index) => (
                  <motion.div
                    key={section.id}
                    initial={{
                      opacity: 0,
                      scale: 0.8,
                      y: 50,
                      filter: 'blur(10px)'
                    }}
                    animate={{
                      opacity: 1,
                      scale: 1,
                      y: 0,
                      filter: 'blur(0px)'
                    }}
                    transition={{
                      duration: 1.5,
                      delay: 1.5 + index * 0.3,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                  >
                    <SectionCard
                      section={section}
                      index={index}
                      delay={0}
                    />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

    </div >
  )
}

export default Home
