import { motion } from 'framer-motion'
import { useEffect } from 'react'

const SupernaturalGlitch = ({ text, isActive, onComplete }) => {
  // Force font loading
  useEffect(() => {
    const link = document.createElement('link')
    link.href = 'https://fonts.googleapis.com/css2?family=Black+Ops+One:wght@400&display=swap'
    link.rel = 'stylesheet'
    document.head.appendChild(link)

    // Preload the font
    const fontFace = new FontFace('Black Ops One', 'url(https://fonts.gstatic.com/s/blackopsone/v20/qWcsB6-ypo7xBdr6Xshe96H3WDzRtjkho4M.woff2)')
    fontFace.load().then(() => {
      document.fonts.add(fontFace)
    }).catch(err => console.log('Font loading failed:', err))
  }, [])
  const glitchVariants = {
    normal: {
      x: 0,
      y: 0,
      skew: 0,
      scale: 1,
      opacity: 1,
      filter: 'brightness(1) contrast(1) hue-rotate(0deg)',
      textShadow: '0 0 30px rgba(255, 255, 255, 0.3)'
    },
    glitch: {
      x: [0, -8, 5, -3, 7, -2, 4, 0],
      y: [0, 3, -5, 8, -2, 6, -4, 0],
      skew: [0, 3, -2, 5, -4, 2, -1, 0],
      scale: [1, 1.05, 0.95, 1.08, 0.92, 1.03, 0.97, 1],
      opacity: [1, 0.7, 1, 0.4, 1, 0.6, 1, 1],
      filter: [
        'brightness(1) contrast(1) hue-rotate(0deg)',
        'brightness(1.5) contrast(1.3) hue-rotate(5deg)',
        'brightness(0.6) contrast(1.1) hue-rotate(-3deg)',
        'brightness(1.8) contrast(1.5) hue-rotate(8deg)',
        'brightness(0.4) contrast(0.9) hue-rotate(-5deg)',
        'brightness(1.3) contrast(1.2) hue-rotate(3deg)',
        'brightness(0.8) contrast(1.1) hue-rotate(-2deg)',
        'brightness(1) contrast(1) hue-rotate(0deg)'
      ],
      textShadow: [
        '0 0 30px rgba(255, 255, 255, 0.3)',
        '8px 0 rgba(255, 255, 255, 0.8), -8px 0 rgba(200, 200, 200, 0.6)',
        '-5px 0 rgba(255, 255, 255, 0.9), 5px 0 rgba(180, 180, 180, 0.7)',
        '3px 0 rgba(255, 255, 255, 1), -3px 0 rgba(220, 220, 220, 0.8)',
        '-7px 0 rgba(255, 255, 255, 0.7), 7px 0 rgba(160, 160, 160, 0.5)',
        '2px 0 rgba(255, 255, 255, 0.9), -2px 0 rgba(240, 240, 240, 0.6)',
        '-4px 0 rgba(255, 255, 255, 0.8), 4px 0 rgba(190, 190, 190, 0.4)',
        '0 0 30px rgba(255, 255, 255, 0.3)'
      ]
    }
  }

  return (
    <div className="relative">
      {/* Main glitching text */}
      <motion.div
        className="text-6xl md:text-8xl text-white select-none"
        style={{
          fontFamily: 'Tisk, "Black Ops One", "Orbitron", Impact, "Arial Black", Helvetica, sans-serif',
          letterSpacing: '0.2em',
          fontWeight: 'normal',
          textTransform: 'uppercase',
          textShadow: '3px 3px 0px rgba(0,0,0,0.7)',
          fontStyle: 'normal',
          fontSize: 'clamp(3rem, 8vw, 6rem)'
        }}
        variants={glitchVariants}
        animate={isActive ? 'glitch' : 'normal'}
        transition={{
          duration: 0.15,
          repeat: isActive ? 13 : 0, // 2 seconds of glitching (13 * 0.15 ≈ 2s)
          ease: "easeInOut"
        }}
        onAnimationComplete={() => {
          if (isActive && onComplete) {
            setTimeout(onComplete, 100)
          }
        }}
      >
        {text}
      </motion.div>

      {/* Ghostly afterimage layers */}
      {isActive && (
        <>
          <motion.div
            className="absolute inset-0 text-6xl md:text-8xl text-white select-none"
            style={{
              fontFamily: 'Tisk, "Black Ops One", Impact, "Arial Black", Helvetica, sans-serif',
              letterSpacing: '0.2em',
              fontWeight: 'normal',
              textTransform: 'uppercase',
              opacity: 0.3,
              filter: 'blur(2px)',
              fontSize: 'clamp(3rem, 8vw, 6rem)'
            }}
            animate={{
              x: [0, 6, -4, 8, -6, 3, -5, 0],
              y: [0, -3, 5, -7, 4, -2, 6, 0],
              opacity: [0.3, 0.6, 0.2, 0.7, 0.1, 0.5, 0.3, 0.3]
            }}
            transition={{
              duration: 0.2,
              repeat: 10,
              ease: "easeInOut"
            }}
          >
            {text}
          </motion.div>

          <motion.div
            className="absolute inset-0 text-6xl md:text-8xl text-white select-none"
            style={{
              fontFamily: 'Tisk, "Black Ops One", Impact, "Arial Black", Helvetica, sans-serif',
              letterSpacing: '0.2em',
              fontWeight: 'normal',
              textTransform: 'uppercase',
              opacity: 0.2,
              filter: 'blur(4px)',
              fontSize: 'clamp(3rem, 8vw, 6rem)'
            }}
            animate={{
              x: [0, -5, 7, -3, 6, -8, 2, 0],
              y: [0, 4, -6, 9, -3, 5, -7, 0],
              opacity: [0.2, 0.5, 0.1, 0.6, 0.15, 0.4, 0.2, 0.2]
            }}
            transition={{
              duration: 0.25,
              repeat: 8,
              ease: "easeInOut"
            }}
          >
            {text}
          </motion.div>
        </>
      )}

      {/* Supernatural energy flashes */}
      {isActive && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0, 0.4, 0, 0.6, 0, 0.3, 0, 0.5, 0],
            scale: [1, 1.1, 1, 1.15, 1, 1.08, 1, 1.12, 1]
          }}
          transition={{
            duration: 2,
            ease: "easeInOut"
          }}
          style={{
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent 60%)',
            filter: 'blur(20px)'
          }}
        />
      )}

      {/* Flickering overlay */}
      {isActive && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          animate={{
            opacity: [0, 0.2, 0, 0.15, 0, 0.25, 0, 0.1, 0],
            background: [
              'rgba(255, 255, 255, 0)',
              'rgba(255, 255, 255, 0.05)',
              'rgba(255, 255, 255, 0)',
              'rgba(255, 255, 255, 0.08)',
              'rgba(255, 255, 255, 0)',
              'rgba(255, 255, 255, 0.03)',
              'rgba(255, 255, 255, 0)',
              'rgba(255, 255, 255, 0.06)',
              'rgba(255, 255, 255, 0)'
            ]
          }}
          transition={{
            duration: 2,
            ease: "linear"
          }}
        />
      )}
    </div>
  )
}

export default SupernaturalGlitch
