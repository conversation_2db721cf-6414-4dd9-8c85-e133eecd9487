@import url('https://fonts.googleapis.com/css2?family=Creepster&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nosifer&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Butcherman&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Black+Ops+One:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Roboto', system-ui, sans-serif;
    line-height: 1.6;
    background: #000000;
    color: #ffffff;
    overflow-x: hidden;
  }

  /* Enhanced smoke/fog background with neon accents */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(ellipse at 30% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%),
      radial-gradient(ellipse at 70% 80%, rgba(200, 200, 200, 0.04) 0%, transparent 70%),
      radial-gradient(ellipse at 20% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
      radial-gradient(ellipse at 80% 30%, rgba(0, 255, 252, 0.02) 0%, transparent 40%),
      radial-gradient(ellipse at 40% 90%, rgba(255, 0, 247, 0.015) 0%, transparent 45%),
      radial-gradient(ellipse at 60% 10%, rgba(255, 221, 0, 0.01) 0%, transparent 35%);
    animation: enhancedSmokeFloat 25s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
  }

  /* Noise/glitch background */
  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.02'/%3E%3C/svg%3E");
    animation: noiseShift 3s linear infinite;
    pointer-events: none;
    z-index: -1;
  }

  /* Floating neon particles in smoke */
  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(2px 2px at 20% 30%, #00fffc, transparent),
      radial-gradient(2px 2px at 40% 70%, #ff00f7, transparent),
      radial-gradient(1px 1px at 90% 40%, #ffdd00, transparent),
      radial-gradient(1px 1px at 60% 90%, #8a2be2, transparent),
      radial-gradient(2px 2px at 80% 10%, #00fffc, transparent),
      radial-gradient(1px 1px at 10% 80%, #ff00f7, transparent);
    background-size: 300px 300px, 400px 400px, 200px 200px, 350px 350px, 250px 250px, 180px 180px;
    animation: neonParticleFloat 30s linear infinite;
    opacity: 0.4;
    pointer-events: none;
    z-index: -1;
  }
}

@layer components {
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .container-max {
    @apply max-w-7xl mx-auto;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-neon-blue to-neon-purple text-black font-bold py-3 px-8 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg;
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
  }

  .btn-primary:hover {
    box-shadow: 0 0 30px rgba(0, 245, 255, 0.5), 0 0 40px rgba(191, 0, 255, 0.3);
  }

  .btn-secondary {
    @apply border-2 border-neon-blue text-neon-blue font-medium py-3 px-8 rounded-lg transition-all duration-300 hover:bg-neon-blue hover:text-black;
    box-shadow: 0 0 10px rgba(0, 245, 255, 0.2);
  }

  .btn-secondary:hover {
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.4);
  }

  .text-gradient {
    @apply bg-gradient-to-r from-neon-cyan via-neon-magenta to-neon-yellow bg-clip-text text-transparent;
    animation: neonPulse 3s ease-in-out infinite alternate;
  }

  .title-font {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
  }

  .tech-font {
    font-family: 'Exo 2', sans-serif;
  }

  /* Tisk Font Styles for Main Title */
  .tisk-title {
    font-family: 'Tisk', 'Black Ops One', 'Impact', 'Arial Black', sans-serif !important;
    font-weight: normal;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    text-shadow: 3px 3px 0px rgba(0, 0, 0, 0.7);
    font-style: normal;
    letter-spacing: 0.2em;
    text-transform: uppercase;
  }

  /* Section-specific neon colors */
  .sketch-theme {
    color: #00fffc;
  }

  .art3d-theme {
    color: #ff00f7;
  }

  .unity-theme {
    color: #ffdd00;
  }

  .scenario-theme {
    color: #8a2be2;
  }

  .glitch-text {
    position: relative;
    color: #fff;
    font-weight: bold;
  }

  .glitch-text::before,
  .glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch-text::before {
    animation: glitch 2s infinite;
    color: #ff0040;
    z-index: -1;
  }

  .glitch-text::after {
    animation: glitch 3s infinite;
    color: #00ffff;
    z-index: -2;
  }

  .neon-border {
    border: 2px solid #00f5ff;
    box-shadow:
      0 0 10px #00f5ff,
      inset 0 0 10px rgba(0, 245, 255, 0.1);
  }

  .dark-card {
    @apply bg-dark-100 border border-dark-200 backdrop-blur-sm;
    background: rgba(39, 39, 42, 0.8);
  }

  .dark-card:hover {
    @apply border-neon-blue;
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.2);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #18181b;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00f5ff, #bf00ff);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #00ffff, #ff00ff);
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
}

/* Matrix rain particles */
.matrix-rain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.matrix-char {
  position: absolute;
  color: #00f5ff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  opacity: 0.7;
  animation: matrixRain 10s linear infinite;
}

/* Glitch effects */
.glitch-container {
  position: relative;
  overflow: hidden;
}

.glitch-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 0, 64, 0.03) 50%,
      transparent 100%);
  animation: scanLine 2s linear infinite;
}

/* Neon glow effects */
.neon-glow {
  filter: drop-shadow(0 0 10px currentColor);
}

.neon-text {
  color: #00f5ff;
  text-shadow:
    0 0 5px #00f5ff,
    0 0 10px #00f5ff,
    0 0 15px #00f5ff,
    0 0 20px #00f5ff;
}

/* Particle effects */
@keyframes particle-float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-30px) rotate(120deg);
  }

  66% {
    transform: translateY(30px) rotate(240deg);
  }
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #00f5ff;
  border-radius: 50%;
  animation: particle-float 6s ease-in-out infinite;
  box-shadow: 0 0 10px #00f5ff;
}