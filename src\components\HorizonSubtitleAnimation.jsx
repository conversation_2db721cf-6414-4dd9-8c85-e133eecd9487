import { motion, AnimatePresence } from 'framer-motion'
import { useState, useEffect } from 'react'

const HorizonSubtitleAnimation = ({ 
  text, 
  isVisible, 
  onComplete, 
  delay = 0 
}) => {
  const [particles, setParticles] = useState([])
  const [showText, setShowText] = useState(false)
  const [textComplete, setTextComplete] = useState(false)
  const words = text.split(' ')

  useEffect(() => {
    if (isVisible) {
      // Create particles for each word
      const wordParticles = words.map((word, wordIndex) => {
        return Array.from({ length: 8 }, (_, particleIndex) => ({
          id: `${wordIndex}-${particleIndex}`,
          word,
          wordIndex,
          particleIndex,
          // Start positions (from different directions)
          startX: wordIndex % 2 === 0 
            ? -100 - Math.random() * 200 
            : window.innerWidth + 100 + Math.random() * 200,
          startY: Math.random() * window.innerHeight,
          // Target positions
          targetX: (window.innerWidth / 2) - (words.length * 60) + wordIndex * 120,
          targetY: window.innerHeight / 2 + 80,
          // Particle properties
          size: 1.5 + Math.random() * 3,
          opacity: 0.4 + Math.random() * 0.4,
          delay: delay + wordIndex * 0.2 + particleIndex * 0.03,
          duration: 1.2 + Math.random() * 0.4,
          glowIntensity: 0.3 + Math.random() * 0.4
        }))
      }).flat()
      
      setParticles(wordParticles)

      // Show the final text after particles converge
      const textTimer = setTimeout(() => {
        setShowText(true)
      }, delay + 1800)

      // Mark text as complete
      const completeTimer = setTimeout(() => {
        setTextComplete(true)
        if (onComplete) onComplete()
      }, delay + 4000)

      return () => {
        clearTimeout(textTimer)
        clearTimeout(completeTimer)
      }
    }
  }, [isVisible, text, delay])

  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* Particle Formation Animation */}
      <AnimatePresence>
        {isVisible && !showText && (
          <div className="absolute inset-0">
            {particles.map((particle) => (
              <motion.div
                key={particle.id}
                className="absolute rounded-full"
                style={{
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  background: `radial-gradient(circle, rgba(255, 255, 255, ${particle.opacity}), rgba(255, 0, 247, ${particle.opacity * 0.6}), transparent)`,
                  filter: 'blur(0.5px)',
                  boxShadow: `0 0 ${particle.size * 2}px rgba(255, 255, 255, ${particle.glowIntensity}), 0 0 ${particle.size * 4}px rgba(255, 0, 247, ${particle.glowIntensity * 0.4})`
                }}
                initial={{
                  x: particle.startX,
                  y: particle.startY,
                  opacity: 0,
                  scale: 0
                }}
                animate={{
                  x: particle.targetX + (particle.particleIndex % 3 - 1) * 10,
                  y: particle.targetY + (Math.floor(particle.particleIndex / 3) - 1) * 15,
                  opacity: particle.opacity,
                  scale: 1
                }}
                transition={{
                  duration: particle.duration,
                  delay: particle.delay,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
              />
            ))}
          </div>
        )}
      </AnimatePresence>

      {/* Final Text Formation */}
      <AnimatePresence>
        {showText && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Main Subtitle Text */}
            <motion.div
              style={{
                fontFamily: 'Nosifer, "Ithornët", "Butcherman", serif',
                fontSize: 'clamp(1.5rem, 4vw, 3rem)',
                fontWeight: 'bold',
                letterSpacing: '0.08em',
                textTransform: 'uppercase',
                color: '#ffffff',
                textShadow: '0 0 15px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 0, 247, 0.3)',
                filter: 'drop-shadow(0 0 8px rgba(255, 0, 247, 0.4))'
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {words.map((word, wordIndex) => (
                <motion.span
                  key={wordIndex}
                  initial={{ opacity: 0, y: 15, rotateX: -90 }}
                  animate={{ opacity: 1, y: 0, rotateX: 0 }}
                  transition={{
                    duration: 0.4,
                    delay: 0.3 + wordIndex * 0.1,
                    ease: "easeOut"
                  }}
                  style={{ 
                    display: 'inline-block',
                    marginRight: wordIndex < words.length - 1 ? '0.5em' : '0'
                  }}
                >
                  {word.split('').map((letter, letterIndex) => (
                    <motion.span
                      key={letterIndex}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{
                        duration: 0.2,
                        delay: 0.4 + wordIndex * 0.1 + letterIndex * 0.02,
                        ease: "easeOut"
                      }}
                      style={{ display: 'inline-block' }}
                    >
                      {letter}
                    </motion.span>
                  ))}
                </motion.span>
              ))}
            </motion.div>

            {/* Decorative Lines */}
            <div className="flex justify-center items-center mt-6 space-x-4">
              <motion.div
                style={{
                  width: '60px',
                  height: '1px',
                  background: 'linear-gradient(90deg, transparent, rgba(255, 0, 247, 0.8), rgba(255, 255, 255, 0.6))',
                  filter: 'blur(0.5px)',
                  boxShadow: '0 0 8px rgba(255, 0, 247, 0.5)'
                }}
                initial={{ scaleX: 0, opacity: 0 }}
                animate={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 1.2, ease: "easeOut" }}
              />
              
              <motion.div
                className="w-2 h-2 rounded-full"
                style={{
                  background: 'radial-gradient(circle, rgba(255, 255, 255, 0.9), rgba(255, 0, 247, 0.6))',
                  boxShadow: '0 0 10px rgba(255, 0, 247, 0.8)'
                }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.4, delay: 1.5, ease: "easeOut" }}
              />
              
              <motion.div
                style={{
                  width: '60px',
                  height: '1px',
                  background: 'linear-gradient(90deg, rgba(255, 255, 255, 0.6), rgba(255, 0, 247, 0.8), transparent)',
                  filter: 'blur(0.5px)',
                  boxShadow: '0 0 8px rgba(255, 0, 247, 0.5)'
                }}
                initial={{ scaleX: 0, opacity: 0 }}
                animate={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 1.2, ease: "easeOut" }}
              />
            </div>

            {/* Ambient Glow */}
            <motion.div
              className="absolute inset-0 pointer-events-none"
              style={{
                background: 'radial-gradient(ellipse at center, rgba(255, 0, 247, 0.08), transparent 70%)',
                filter: 'blur(30px)'
              }}
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1.2 }}
              transition={{ duration: 1.5, delay: 0.8, ease: "easeOut" }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Energy Pulse Effect */}
      <AnimatePresence>
        {textComplete && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 0.6, 0] }}
            transition={{ duration: 2, ease: "easeInOut" }}
          >
            <motion.div
              className="absolute inset-0"
              style={{
                background: 'radial-gradient(circle at center, rgba(255, 0, 247, 0.2), transparent 50%)',
                filter: 'blur(20px)'
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.8, 0.3]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default HorizonSubtitleAnimation
