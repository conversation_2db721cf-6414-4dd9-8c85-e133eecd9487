import { motion, AnimatePresence } from 'framer-motion'
import { useState, useEffect } from 'react'

const HorizonTitleAnimation = ({
  text,
  isVisible,
  onComplete,
  fontSize = "clamp(3rem, 8vw, 6rem)",
  fontFamily = '<PERSON>si<PERSON>, "Ithornë<PERSON>", "Butcherman", serif',
  delay = 0
}) => {
  const [particles, setParticles] = useState([])
  const [showText, setShowText] = useState(false)
  const letters = text.split('')

  useEffect(() => {
    if (isVisible) {
      // Create particles for each letter
      const letterParticles = letters.map((letter, letterIndex) => {
        if (letter === ' ') return []

        // Create multiple particles per letter
        return Array.from({ length: 12 }, (_, particleIndex) => ({
          id: `${letterIndex}-${particleIndex}`,
          letter,
          letterIndex,
          particleIndex,
          // Start positions (scattered around the screen)
          startX: Math.random() * window.innerWidth,
          startY: Math.random() * window.innerHeight,
          // Target positions (where the letter should be)
          targetX: letterIndex * 60 + (window.innerWidth / 2) - (letters.length * 30),
          targetY: window.innerHeight / 2,
          // Particle properties
          size: 2 + Math.random() * 4,
          opacity: 0.6 + Math.random() * 0.4,
          delay: delay + letterIndex * 0.1 + particleIndex * 0.02,
          duration: 1.5 + Math.random() * 0.5,
          glowIntensity: 0.5 + Math.random() * 0.5
        }))
      }).flat().filter(Boolean)

      setParticles(letterParticles)

      // Show the final text after particles converge
      const textTimer = setTimeout(() => {
        setShowText(true)
      }, delay + 2000)

      // Complete animation
      const completeTimer = setTimeout(() => {
        if (onComplete) onComplete()
      }, delay + 4000)

      return () => {
        clearTimeout(textTimer)
        clearTimeout(completeTimer)
      }
    }
  }, [isVisible, text, delay])

  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* Particle Formation Animation */}
      <AnimatePresence>
        {isVisible && !showText && (
          <div className="absolute inset-0">
            {particles.map((particle) => (
              <motion.div
                key={particle.id}
                className="absolute rounded-full"
                style={{
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  background: `radial-gradient(circle, rgba(255, 255, 255, ${particle.opacity}), rgba(0, 255, 252, ${particle.opacity * 0.8}), transparent)`,
                  filter: 'blur(1px)',
                  boxShadow: `0 0 ${particle.size * 3}px rgba(255, 255, 255, ${particle.glowIntensity}), 0 0 ${particle.size * 6}px rgba(0, 255, 252, ${particle.glowIntensity * 0.5})`
                }}
                initial={{
                  x: particle.startX,
                  y: particle.startY,
                  opacity: 0,
                  scale: 0
                }}
                animate={{
                  x: particle.targetX + (particle.particleIndex % 4 - 2) * 15,
                  y: particle.targetY + (Math.floor(particle.particleIndex / 4) - 1.5) * 20,
                  opacity: particle.opacity,
                  scale: 1
                }}
                transition={{
                  duration: particle.duration,
                  delay: particle.delay,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
              />
            ))}
          </div>
        )}
      </AnimatePresence>

      {/* Final Text Formation */}
      <AnimatePresence>
        {showText && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {/* Main Text */}
            <motion.div
              style={{
                fontFamily,
                fontSize,
                fontWeight: 'bold',
                letterSpacing: '0.08em',
                textTransform: 'uppercase',
                color: '#ffffff',
                textShadow: '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4), 0 0 30px rgba(0, 255, 252, 0.3)',
                filter: 'drop-shadow(0 0 10px rgba(0, 255, 252, 0.5))'
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {letters.map((letter, index) => (
                <motion.span
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.3,
                    delay: 0.3 + index * 0.05,
                    ease: "easeOut"
                  }}
                  style={{ display: 'inline-block' }}
                >
                  {letter === ' ' ? '\u00A0' : letter}
                </motion.span>
              ))}
            </motion.div>

            {/* Glowing Underline Effect */}
            <motion.div
              className="mt-4"
              style={{
                height: '2px',
                background: 'linear-gradient(90deg, transparent, rgba(0, 255, 252, 0.8), transparent)',
                filter: 'blur(1px)',
                boxShadow: '0 0 10px rgba(0, 255, 252, 0.6)'
              }}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 1, delay: 1, ease: "easeOut" }}
            />

            {/* Ambient Glow */}
            <motion.div
              className="absolute inset-0 pointer-events-none"
              style={{
                background: 'radial-gradient(ellipse at center, rgba(0, 255, 252, 0.1), transparent 60%)',
                filter: 'blur(40px)'
              }}
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1.5 }}
              transition={{ duration: 2, delay: 0.5, ease: "easeOut" }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Energy Burst Effect */}
      <AnimatePresence>
        {showText && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 0.8, 0] }}
            transition={{ duration: 1.5, delay: 0.8, ease: "easeOut" }}
          >
            {Array.from({ length: 20 }, (_, index) => (
              <motion.div
                key={index}
                className="absolute"
                style={{
                  width: '2px',
                  height: `${20 + Math.random() * 40}px`,
                  background: 'linear-gradient(to bottom, rgba(0, 255, 252, 0.8), transparent)',
                  filter: 'blur(1px)',
                  left: `${45 + index * 2}%`,
                  top: '45%',
                  transformOrigin: 'bottom center'
                }}
                initial={{ scaleY: 0, opacity: 0 }}
                animate={{
                  scaleY: [0, 1, 0],
                  opacity: [0, 0.8, 0],
                  rotate: (Math.random() - 0.5) * 30
                }}
                transition={{
                  duration: 1,
                  delay: 0.8 + index * 0.05,
                  ease: "easeOut"
                }}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default HorizonTitleAnimation
