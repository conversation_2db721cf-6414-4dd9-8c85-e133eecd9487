import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'

const HauntedSubtitle = ({ text, isVisible, onComplete }) => {
  const [animationPhase, setAnimationPhase] = useState('falling') // falling, visible, shattering
  const letters = text.split('')

  useEffect(() => {
    if (isVisible) {
      // After falling (1.2s), stay visible for 2 seconds, then shatter
      const visibleTimer = setTimeout(() => {
        setAnimationPhase('visible')
      }, 1200)

      const shatterTimer = setTimeout(() => {
        setAnimationPhase('shattering')
      }, 3200) // 1.2s fall + 2s visible = 3.2s

      return () => {
        clearTimeout(visibleTimer)
        clearTimeout(shatterTimer)
      }
    }
  }, [isVisible])

  return (
    <div className="relative">
      {/* Main subtitle - falling and visible phases */}
      {animationPhase !== 'shattering' && (
        <motion.div
          className="ithornet-fallback text-3xl md:text-5xl font-bold text-white text-center"
          style={{
            fontFamily: '<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, serif',
            letterSpacing: '0.05em',
            fontWeight: 'bold'
          }}
          initial={{
            y: -window.innerHeight - 100,
            opacity: 1,
            scale: 1.1
          }}
          animate={isVisible ? {
            y: 0,
            opacity: 1,
            scale: 1,
            filter: 'blur(0px)'
          } : {}}
          transition={{
            duration: 1.2,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
        >
          {text}
        </motion.div>
      )}

      {/* Shattering letters */}
      {animationPhase === 'shattering' && (
        <div className="absolute inset-0 flex flex-wrap justify-center items-center">
          {letters.map((letter, index) => {
            const fragments = Array.from({ length: 6 }, (_, fragIndex) => {
              const angle = (fragIndex / 6) * Math.PI * 2
              const distance = 200 + Math.random() * 300
              return {
                id: fragIndex,
                x: Math.cos(angle) * distance,
                y: Math.sin(angle) * distance,
                rotation: Math.random() * 720 + 360,
                scale: 0.3 + Math.random() * 0.7,
                delay: index * 0.05 + fragIndex * 0.02
              }
            })

            return (
              <div key={index} className="relative inline-block">
                {/* Original letter that disappears */}
                <motion.span
                  className="ithornet-fallback text-3xl md:text-5xl font-bold text-white"
                  style={{
                    fontFamily: 'Ithornet, Butcherman, Nosifer, serif',
                    letterSpacing: '0.05em',
                    fontWeight: 'bold'
                  }}
                  initial={{ opacity: 1, scale: 1 }}
                  animate={{
                    opacity: 0,
                    scale: 1.2
                  }}
                  transition={{
                    duration: 0.2,
                    delay: index * 0.05
                  }}
                >
                  {letter === ' ' ? '\u00A0' : letter}
                </motion.span>

                {/* Letter fragments */}
                {fragments.map((fragment) => (
                  <motion.span
                    key={fragment.id}
                    className="absolute top-0 left-0 ithornet-fallback text-lg md:text-2xl font-bold text-white"
                    style={{
                      fontFamily: 'Ithornet, Butcherman, Nosifer, serif',
                      letterSpacing: '0.05em',
                      fontWeight: 'bold'
                    }}
                    initial={{
                      x: 0,
                      y: 0,
                      opacity: 1,
                      scale: fragment.scale,
                      rotate: 0,
                      filter: 'blur(0px)'
                    }}
                    animate={{
                      x: fragment.x,
                      y: fragment.y,
                      opacity: [1, 0.8, 0.4, 0],
                      rotate: fragment.rotation,
                      scale: [fragment.scale, fragment.scale * 0.7, fragment.scale * 0.3, 0],
                      filter: ['blur(0px)', 'blur(1px)', 'blur(3px)', 'blur(6px)']
                    }}
                    transition={{
                      duration: 2.5,
                      delay: fragment.delay,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                  >
                    {letter === ' ' ? '\u00A0' : letter}
                  </motion.span>
                ))}
              </div>
            )
          })}
        </div>
      )}

      {/* Ghostly smoke interaction during shattering */}
      {animationPhase === 'shattering' && (
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: 30 }, (_, index) => (
            <motion.div
              key={index}
              className="absolute w-4 h-4 rounded-full"
              style={{
                background: 'radial-gradient(circle, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1), transparent)',
                filter: 'blur(3px)',
                left: `${10 + (index * 2.5)}%`,
                top: `${40 + Math.random() * 20}%`
              }}
              initial={{
                opacity: 0,
                scale: 0,
                y: 0
              }}
              animate={{
                opacity: [0, 0.8, 0.5, 0.2, 0],
                scale: [0, 1.5, 2, 3, 4],
                y: [0, -30, -60, -100, -150],
                x: [(Math.random() - 0.5) * 100, (Math.random() - 0.5) * 200]
              }}
              transition={{
                duration: 3,
                delay: index * 0.05,
                ease: "easeOut"
              }}
            />
          ))}
        </div>
      )}

      {/* Supernatural energy explosion during shattering */}
      {animationPhase === 'shattering' && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0, 0.3, 0.6, 0.4, 0.1, 0],
            scale: [1, 1.5, 2, 2.5, 3, 4]
          }}
          transition={{
            duration: 2.5,
            ease: "easeOut"
          }}
          style={{
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.08), transparent 60%)',
            filter: 'blur(40px)'
          }}
        />
      )}

      {/* Completion callback */}
      {animationPhase === 'shattering' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          onAnimationComplete={() => {
            setTimeout(() => {
              if (onComplete) onComplete()
            }, 3000) // Wait for shattering to complete
          }}
        />
      )}
    </div>
  )
}

export default HauntedSubtitle
